<?php
/**
 * CFB Calculator Settings
 * Handles plugin settings and configuration
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Settings {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_init', array($this, 'handle_settings_save'));
        add_action('wp_ajax_cfb_save_settings', array($this, 'ajax_save_settings'));
    }
    
    /**
     * Register plugin settings
     */
    public function register_settings() {
        // Currency Settings
        register_setting('cfb_calculator_settings', 'cfb_currency_symbol');
        register_setting('cfb_calculator_settings', 'cfb_currency_position');
        register_setting('cfb_calculator_settings', 'cfb_decimal_places');
        register_setting('cfb_calculator_settings', 'cfb_thousand_separator');
        register_setting('cfb_calculator_settings', 'cfb_decimal_separator');
        
        // Display Settings
        register_setting('cfb_calculator_settings', 'cfb_default_theme');
        register_setting('cfb_calculator_settings', 'cfb_enable_animations');
        register_setting('cfb_calculator_settings', 'cfb_auto_calculate');
        
        // Language Settings
        register_setting('cfb_calculator_settings', 'cfb_rtl_support');
        register_setting('cfb_calculator_settings', 'cfb_default_language');
        
        // Advanced Settings
        register_setting('cfb_calculator_settings', 'cfb_enable_caching');
        register_setting('cfb_calculator_settings', 'cfb_debug_mode');
        register_setting('cfb_calculator_settings', 'cfb_custom_css');

        // PDF & Invoice Settings
        register_setting('cfb_calculator_settings', 'cfb_company_name');
        register_setting('cfb_calculator_settings', 'cfb_company_address');
        register_setting('cfb_calculator_settings', 'cfb_company_phone');
        register_setting('cfb_calculator_settings', 'cfb_company_email');
        register_setting('cfb_calculator_settings', 'cfb_company_website');
        register_setting('cfb_calculator_settings', 'cfb_company_logo');
        register_setting('cfb_calculator_settings', 'cfb_pdf_template');
        register_setting('cfb_calculator_settings', 'cfb_invoice_prefix');
        register_setting('cfb_calculator_settings', 'cfb_payment_terms');
        register_setting('cfb_calculator_settings', 'cfb_pdf_font_family');
        register_setting('cfb_calculator_settings', 'cfb_pdf_font_size');

        // Enhanced PDF Settings
        register_setting('cfb_calculator_settings', 'cfb_pdf_total_position');
        register_setting('cfb_calculator_settings', 'cfb_pdf_tax_enabled');
        register_setting('cfb_calculator_settings', 'cfb_pdf_tax_rate');
        register_setting('cfb_calculator_settings', 'cfb_pdf_tax_title');
        register_setting('cfb_calculator_settings', 'cfb_pdf_title_font_size');
        register_setting('cfb_calculator_settings', 'cfb_pdf_billto_font_size');
        register_setting('cfb_calculator_settings', 'cfb_pdf_payment_font_size');
        register_setting('cfb_calculator_settings', 'cfb_pdf_payment_label');
        register_setting('cfb_calculator_settings', 'cfb_pdf_yes_label');
        register_setting('cfb_calculator_settings', 'cfb_pdf_no_label');
        register_setting('cfb_calculator_settings', 'cfb_pdf_color_scheme');
        register_setting('cfb_calculator_settings', 'cfb_pdf_rtl_support');
        register_setting('cfb_calculator_settings', 'cfb_convert_to_farsi_digits');
        register_setting('cfb_calculator_settings', 'cfb_invoice_title');
    }

    /**
     * Handle settings form submission
     */
    public function handle_settings_save() {
        // Check if this is our settings form submission
        if (!isset($_POST['action']) || $_POST['action'] !== 'cfb_save_settings') {
            return;
        }

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized access', 'cfb-calculator'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['cfb_settings_nonce'], 'cfb_settings_nonce')) {
            wp_die(__('Security check failed', 'cfb-calculator'));
        }

        // Process all form fields
        $settings_fields = array(
            'cfb_currency_symbol',
            'cfb_currency_position',
            'cfb_decimal_places',
            'cfb_thousand_separator',
            'cfb_decimal_separator',
            'cfb_default_theme',
            'cfb_enable_animations',
            'cfb_auto_calculate',
            'cfb_rtl_support',
            'cfb_default_language',
            'cfb_enable_caching',
            'cfb_debug_mode',
            'cfb_custom_css',
            'cfb_company_name',
            'cfb_company_address',
            'cfb_company_phone',
            'cfb_company_email',
            'cfb_company_website',
            'cfb_company_logo',
            'cfb_pdf_template',
            'cfb_invoice_prefix',
            'cfb_payment_terms',
            'cfb_pdf_font_family',
            'cfb_pdf_font_size',
            'cfb_pdf_color_scheme',
            'cfb_pdf_rtl_support',
            'cfb_convert_to_farsi_digits',
            'cfb_invoice_title',
            // Enhanced PDF Settings
            'cfb_pdf_total_position',
            'cfb_pdf_tax_enabled',
            'cfb_pdf_tax_rate',
            'cfb_pdf_tax_title',
            'cfb_pdf_title_font_size',
            'cfb_pdf_billto_font_size',
            'cfb_pdf_payment_font_size',
            'cfb_pdf_payment_label',
            'cfb_pdf_yes_label',
            'cfb_pdf_no_label'
        );

        foreach ($settings_fields as $field) {
            $value = isset($_POST[$field]) ? $_POST[$field] : '';

            if ($field === 'cfb_custom_css') {
                update_option($field, wp_kses_post($value));
            } elseif ($field === 'cfb_company_logo') {
                // Handle logo upload separately
                update_option($field, sanitize_url($value));
            } elseif (in_array($field, array('cfb_enable_animations', 'cfb_auto_calculate', 'cfb_rtl_support', 'cfb_enable_caching', 'cfb_debug_mode', 'cfb_pdf_rtl_support', 'cfb_convert_to_farsi_digits'))) {
                update_option($field, $value ? 1 : 0);
            } else {
                update_option($field, sanitize_text_field($value));
            }
        }

        // Redirect back to settings page with success message
        wp_redirect(admin_url('admin.php?page=cfb-calculator-settings&settings-updated=true'));
        exit;
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        // Enqueue media uploader
        wp_enqueue_media();
        ?>
        <div class="wrap cfb-settings-page">
            <h1><?php _e('CFB Calculator Settings', 'cfb-calculator'); ?></h1>

            <?php if (isset($_GET['settings-updated']) && $_GET['settings-updated']): ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php _e('Settings saved successfully!', 'cfb-calculator'); ?></p>
                </div>
            <?php endif; ?>

            <div class="cfb-settings-container">
                <div class="cfb-settings-nav">
                    <ul class="cfb-tabs">
                        <li class="cfb-tab active" data-tab="currency">
                            <span class="dashicons dashicons-money-alt"></span>
                            <?php _e('Currency', 'cfb-calculator'); ?>
                        </li>
                        <li class="cfb-tab" data-tab="display">
                            <span class="dashicons dashicons-admin-appearance"></span>
                            <?php _e('Display', 'cfb-calculator'); ?>
                        </li>
                        <li class="cfb-tab" data-tab="language">
                            <span class="dashicons dashicons-translation"></span>
                            <?php _e('Language', 'cfb-calculator'); ?>
                        </li>
                        <li class="cfb-tab" data-tab="pdf">
                            <span class="dashicons dashicons-media-document"></span>
                            <?php _e('PDF & Invoices', 'cfb-calculator'); ?>
                        </li>
                        <li class="cfb-tab" data-tab="advanced">
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Advanced', 'cfb-calculator'); ?>
                        </li>
                    </ul>
                </div>
                
                <div class="cfb-settings-content">
                    <form id="cfb-settings-form" method="post" action="">
                        <?php wp_nonce_field('cfb_settings_nonce', 'cfb_settings_nonce'); ?>
                        <input type="hidden" name="action" value="cfb_save_settings">
                        
                        <!-- Currency Settings -->
                        <div class="cfb-tab-content active" id="currency-tab">
                            <h2><?php _e('Currency Settings', 'cfb-calculator'); ?></h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_currency_symbol"><?php _e('Currency Symbol', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <input type="text" 
                                               id="cfb_currency_symbol" 
                                               name="cfb_currency_symbol" 
                                               value="<?php echo esc_attr(get_option('cfb_currency_symbol', '$')); ?>" 
                                               class="regular-text">
                                        <p class="description"><?php _e('The symbol to display for currency (e.g., $, €, £, ﷼)', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_currency_position"><?php _e('Currency Position', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <select id="cfb_currency_position" name="cfb_currency_position">
                                            <option value="left" <?php selected(get_option('cfb_currency_position', 'left'), 'left'); ?>>
                                                <?php _e('Left ($100)', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="right" <?php selected(get_option('cfb_currency_position', 'left'), 'right'); ?>>
                                                <?php _e('Right (100$)', 'cfb-calculator'); ?>
                                            </option>
                                        </select>
                                        <p class="description"><?php _e('Position of currency symbol relative to the amount', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_decimal_places"><?php _e('Decimal Places', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <input type="number" 
                                               id="cfb_decimal_places" 
                                               name="cfb_decimal_places" 
                                               value="<?php echo esc_attr(get_option('cfb_decimal_places', 2)); ?>" 
                                               min="0" 
                                               max="4" 
                                               class="small-text">
                                        <p class="description"><?php _e('Number of decimal places to display', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_thousand_separator"><?php _e('Thousand Separator', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <input type="text" 
                                               id="cfb_thousand_separator" 
                                               name="cfb_thousand_separator" 
                                               value="<?php echo esc_attr(get_option('cfb_thousand_separator', ',')); ?>" 
                                               class="small-text">
                                        <p class="description"><?php _e('Character used to separate thousands (e.g., comma, space)', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_decimal_separator"><?php _e('Decimal Separator', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <input type="text" 
                                               id="cfb_decimal_separator" 
                                               name="cfb_decimal_separator" 
                                               value="<?php echo esc_attr(get_option('cfb_decimal_separator', '.')); ?>" 
                                               class="small-text">
                                        <p class="description"><?php _e('Character used to separate decimal places', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <!-- Display Settings -->
                        <div class="cfb-tab-content" id="display-tab">
                            <h2><?php _e('Display Settings', 'cfb-calculator'); ?></h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_default_theme"><?php _e('Default Theme', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <select id="cfb_default_theme" name="cfb_default_theme">
                                            <option value="modern" <?php selected(get_option('cfb_default_theme', 'modern'), 'modern'); ?>>
                                                <?php _e('Modern', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="classic" <?php selected(get_option('cfb_default_theme', 'modern'), 'classic'); ?>>
                                                <?php _e('Classic', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="minimal" <?php selected(get_option('cfb_default_theme', 'modern'), 'minimal'); ?>>
                                                <?php _e('Minimal', 'cfb-calculator'); ?>
                                            </option>
                                        </select>
                                        <p class="description"><?php _e('Default theme for calculator forms', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_enable_animations"><?php _e('Enable Animations', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" 
                                                   id="cfb_enable_animations" 
                                                   name="cfb_enable_animations" 
                                                   value="1" 
                                                   <?php checked(get_option('cfb_enable_animations', 1), 1); ?>>
                                            <?php _e('Enable smooth animations and transitions', 'cfb-calculator'); ?>
                                        </label>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_auto_calculate"><?php _e('Auto Calculate', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" 
                                                   id="cfb_auto_calculate" 
                                                   name="cfb_auto_calculate" 
                                                   value="1" 
                                                   <?php checked(get_option('cfb_auto_calculate', 1), 1); ?>>
                                            <?php _e('Calculate automatically when form values change', 'cfb-calculator'); ?>
                                        </label>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <!-- Language Settings -->
                        <div class="cfb-tab-content" id="language-tab">
                            <h2><?php _e('Language & RTL Settings', 'cfb-calculator'); ?></h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_rtl_support"><?php _e('RTL Support', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" 
                                                   id="cfb_rtl_support" 
                                                   name="cfb_rtl_support" 
                                                   value="1" 
                                                   <?php checked(get_option('cfb_rtl_support', 1), 1); ?>>
                                            <?php _e('Enable Right-to-Left (RTL) language support', 'cfb-calculator'); ?>
                                        </label>
                                        <p class="description"><?php _e('Automatically adjusts layout for RTL languages like Arabic, Hebrew, Farsi', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_default_language"><?php _e('Default Language', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <select id="cfb_default_language" name="cfb_default_language">
                                            <option value="en" <?php selected(get_option('cfb_default_language', 'en'), 'en'); ?>>
                                                <?php _e('English', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="fa" <?php selected(get_option('cfb_default_language', 'en'), 'fa'); ?>>
                                                <?php _e('Farsi (Persian)', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="ar" <?php selected(get_option('cfb_default_language', 'en'), 'ar'); ?>>
                                                <?php _e('Arabic', 'cfb-calculator'); ?>
                                            </option>
                                        </select>
                                        <p class="description"><?php _e('Default language for new forms', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- PDF & Invoice Settings -->
                        <div class="cfb-tab-content" id="pdf-tab">
                            <h2><?php _e('PDF & Invoice Settings', 'cfb-calculator'); ?></h2>

                            <!-- Company Information Section -->
                            <div class="cfb-settings-section">
                                <h3 class="cfb-section-title">
                                    <span class="dashicons dashicons-building"></span>
                                    <?php _e('Company Information', 'cfb-calculator'); ?>
                                </h3>

                                <div class="cfb-settings-grid">
                                    <div class="cfb-setting-item">
                                        <label for="cfb_company_name" class="cfb-setting-label">
                                            <?php _e('Company Name', 'cfb-calculator'); ?> *
                                        </label>
                                        <input type="text"
                                               id="cfb_company_name"
                                               name="cfb_company_name"
                                               value="<?php echo esc_attr(get_option('cfb_company_name', get_bloginfo('name'))); ?>"
                                               class="cfb-setting-input"
                                               required>
                                        <p class="cfb-setting-description"><?php _e('Company name to appear on invoices', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_company_email" class="cfb-setting-label">
                                            <?php _e('Company Email', 'cfb-calculator'); ?> *
                                        </label>
                                        <input type="email"
                                               id="cfb_company_email"
                                               name="cfb_company_email"
                                               value="<?php echo esc_attr(get_option('cfb_company_email', get_option('admin_email'))); ?>"
                                               class="cfb-setting-input"
                                               required>
                                        <p class="cfb-setting-description"><?php _e('Company email address', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_company_phone" class="cfb-setting-label">
                                            <?php _e('Company Phone', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="text"
                                               id="cfb_company_phone"
                                               name="cfb_company_phone"
                                               value="<?php echo esc_attr(get_option('cfb_company_phone', '')); ?>"
                                               class="cfb-setting-input">
                                        <p class="cfb-setting-description"><?php _e('Company phone number', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_company_website" class="cfb-setting-label">
                                            <?php _e('Company Website', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="url"
                                               id="cfb_company_website"
                                               name="cfb_company_website"
                                               value="<?php echo esc_attr(get_option('cfb_company_website', get_site_url())); ?>"
                                               class="cfb-setting-input">
                                        <p class="cfb-setting-description"><?php _e('Company website URL', 'cfb-calculator'); ?></p>
                                    </div>
                                </div>

                                <div class="cfb-setting-item cfb-full-width">
                                    <label for="cfb_company_address" class="cfb-setting-label">
                                        <?php _e('Company Address', 'cfb-calculator'); ?>
                                    </label>
                                    <textarea id="cfb_company_address"
                                              name="cfb_company_address"
                                              rows="3"
                                              class="cfb-setting-textarea"><?php echo esc_textarea(get_option('cfb_company_address', '')); ?></textarea>
                                    <p class="cfb-setting-description"><?php _e('Full company address for invoices', 'cfb-calculator'); ?></p>
                                </div>

                                <!-- Company Logo Section -->
                                <div class="cfb-setting-item cfb-full-width">
                                    <label class="cfb-setting-label">
                                        <span class="dashicons dashicons-format-image"></span>
                                        <?php _e('Company Logo', 'cfb-calculator'); ?>
                                    </label>
                                    <div class="cfb-logo-upload-container">
                                        <div class="cfb-logo-preview">
                                            <?php
                                            $logo_url = get_option('cfb_company_logo', '');
                                            if ($logo_url): ?>
                                                <img src="<?php echo esc_url($logo_url); ?>" alt="Company Logo" class="cfb-logo-image">
                                            <?php else: ?>
                                                <div class="cfb-logo-placeholder">
                                                    <span class="dashicons dashicons-format-image"></span>
                                                    <p><?php _e('No logo uploaded', 'cfb-calculator'); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="cfb-logo-controls">
                                            <button type="button" class="button cfb-upload-logo-btn">
                                                <span class="dashicons dashicons-upload"></span>
                                                <?php _e('Upload Logo', 'cfb-calculator'); ?>
                                            </button>
                                            <?php if ($logo_url): ?>
                                                <button type="button" class="button cfb-remove-logo-btn">
                                                    <span class="dashicons dashicons-trash"></span>
                                                    <?php _e('Remove Logo', 'cfb-calculator'); ?>
                                                </button>
                                            <?php endif; ?>
                                            <input type="hidden" id="cfb_company_logo" name="cfb_company_logo" value="<?php echo esc_attr($logo_url); ?>">
                                        </div>
                                        <p class="cfb-setting-description">
                                            <?php _e('Upload your company logo for invoices. Recommended size: 200x80px. Supported formats: JPG, PNG, SVG', 'cfb-calculator'); ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- PDF Design Section -->
                            <div class="cfb-settings-section">
                                <h3 class="cfb-section-title">
                                    <span class="dashicons dashicons-admin-customizer"></span>
                                    <?php _e('PDF Design & Layout', 'cfb-calculator'); ?>
                                </h3>

                                <div class="cfb-settings-grid">
                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_template" class="cfb-setting-label">
                                            <?php _e('PDF Template', 'cfb-calculator'); ?>
                                        </label>
                                        <div class="cfb-template-selector">
                                            <div class="cfb-template-options">
                                                <label class="cfb-template-option <?php echo get_option('cfb_pdf_template', 'modern') === 'modern' ? 'selected' : ''; ?>">
                                                    <input type="radio" name="cfb_pdf_template" value="modern" <?php checked(get_option('cfb_pdf_template', 'modern'), 'modern'); ?>>
                                                    <div class="cfb-template-preview">
                                                        <div class="cfb-template-thumbnail cfb-template-modern"></div>
                                                        <span class="cfb-template-name"><?php _e('Modern', 'cfb-calculator'); ?></span>
                                                    </div>
                                                </label>
                                                <label class="cfb-template-option <?php echo get_option('cfb_pdf_template', 'modern') === 'classic' ? 'selected' : ''; ?>">
                                                    <input type="radio" name="cfb_pdf_template" value="classic" <?php checked(get_option('cfb_pdf_template', 'modern'), 'classic'); ?>>
                                                    <div class="cfb-template-preview">
                                                        <div class="cfb-template-thumbnail cfb-template-classic"></div>
                                                        <span class="cfb-template-name"><?php _e('Classic', 'cfb-calculator'); ?></span>
                                                    </div>
                                                </label>
                                                <label class="cfb-template-option <?php echo get_option('cfb_pdf_template', 'modern') === 'minimal' ? 'selected' : ''; ?>">
                                                    <input type="radio" name="cfb_pdf_template" value="minimal" <?php checked(get_option('cfb_pdf_template', 'modern'), 'minimal'); ?>>
                                                    <div class="cfb-template-preview">
                                                        <div class="cfb-template-thumbnail cfb-template-minimal"></div>
                                                        <span class="cfb-template-name"><?php _e('Minimal', 'cfb-calculator'); ?></span>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                        <p class="cfb-setting-description"><?php _e('Choose the PDF template design', 'cfb-calculator'); ?></p>
                                    </div>
                                </div>

                                <div class="cfb-settings-grid">
                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_font_family" class="cfb-setting-label">
                                            <?php _e('Font Family', 'cfb-calculator'); ?>
                                        </label>
                                        <select id="cfb_pdf_font_family" name="cfb_pdf_font_family" class="cfb-setting-select">
                                            <option value="helvetica" <?php selected(get_option('cfb_pdf_font_family', 'helvetica'), 'helvetica'); ?>>
                                                Helvetica (Default)
                                            </option>
                                            <option value="times" <?php selected(get_option('cfb_pdf_font_family', 'helvetica'), 'times'); ?>>
                                                Times New Roman
                                            </option>
                                            <option value="courier" <?php selected(get_option('cfb_pdf_font_family', 'helvetica'), 'courier'); ?>>
                                                Courier
                                            </option>
                                            <option value="dejavusans" <?php selected(get_option('cfb_pdf_font_family', 'helvetica'), 'dejavusans'); ?>>
                                                DejaVu Sans (Unicode Support)
                                            </option>
                                            <optgroup label="<?php _e('Persian Fonts', 'cfb-calculator'); ?>">
                                                <?php
                                                $persian_fonts = [
                                                    'xyekan' => 'XYekan (Persian)',
                                                    'xnazanin' => 'XNazanin (Persian)',
                                                    'xzar' => 'XZar (Persian)'
                                                ];

                                                foreach ($persian_fonts as $font_key => $font_name) {
                                                    $font_available = $this->is_persian_font_available($font_key);
                                                    $font_label = $font_available ? $font_name : $font_name . ' (Not Installed)';
                                                    $disabled = $font_available ? '' : 'disabled';
                                                    ?>
                                                    <option value="<?php echo esc_attr($font_key); ?>"
                                                            <?php selected(get_option('cfb_pdf_font_family', 'helvetica'), $font_key); ?>
                                                            <?php echo $disabled; ?>>
                                                        <?php echo esc_html($font_label); ?>
                                                    </option>
                                                    <?php
                                                }
                                                ?>
                                            </optgroup>
                                        </select>
                                        <p class="cfb-setting-description"><?php _e('Font family for PDF content', 'cfb-calculator'); ?></p>

                                        <?php
                                        // Show Persian font status
                                        $persian_fonts_status = [];
                                        $persian_fonts = ['xyekan' => 'XYekan', 'xnazanin' => 'XNazanin', 'xzar' => 'XZar'];
                                        $installed_count = 0;

                                        foreach ($persian_fonts as $font_key => $font_name) {
                                            $available = $this->is_persian_font_available($font_key);
                                            $persian_fonts_status[$font_key] = $available;
                                            if ($available) $installed_count++;
                                        }

                                        if ($installed_count > 0) {
                                            echo '<div class="cfb-font-status" style="margin-top: 10px; padding: 10px; background: #d1f2eb; border: 1px solid #a3e4d7; border-radius: 4px;">';
                                            echo '<p style="margin: 0; color: #0e6b56; font-weight: 600;"><span class="dashicons dashicons-yes-alt"></span> ' . sprintf(__('%d Persian fonts installed', 'cfb-calculator'), $installed_count) . '</p>';
                                            echo '</div>';
                                        } else {
                                            echo '<div class="cfb-font-status" style="margin-top: 10px; padding: 10px; background: #fef9e7; border: 1px solid #f6e05e; border-radius: 4px;">';
                                            echo '<p style="margin: 0 0 5px 0; color: #975a16; font-weight: 600;"><span class="dashicons dashicons-warning"></span> ' . __('No Persian fonts installed', 'cfb-calculator') . '</p>';
                                            echo '<p style="margin: 0; font-size: 12px; color: #975a16;">';
                                            echo __('Install Persian fonts for better RTL text support.', 'cfb-calculator');
                                            echo ' <a href="install-persian-fonts.php" target="_blank" style="color: #975a16; text-decoration: underline;">' . __('Install fonts', 'cfb-calculator') . '</a>';
                                            echo '</p>';
                                            echo '</div>';
                                        }
                                        ?>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_font_size" class="cfb-setting-label">
                                            <?php _e('Font Size', 'cfb-calculator'); ?>
                                        </label>
                                        <select id="cfb_pdf_font_size" name="cfb_pdf_font_size" class="cfb-setting-select">
                                            <option value="8" <?php selected(get_option('cfb_pdf_font_size', '10'), '8'); ?>>8pt</option>
                                            <option value="9" <?php selected(get_option('cfb_pdf_font_size', '10'), '9'); ?>>9pt</option>
                                            <option value="10" <?php selected(get_option('cfb_pdf_font_size', '10'), '10'); ?>>10pt (Default)</option>
                                            <option value="11" <?php selected(get_option('cfb_pdf_font_size', '10'), '11'); ?>>11pt</option>
                                            <option value="12" <?php selected(get_option('cfb_pdf_font_size', '10'), '12'); ?>>12pt</option>
                                        </select>
                                        <p class="cfb-setting-description"><?php _e('Base font size for PDF content', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_color_scheme" class="cfb-setting-label">
                                            <?php _e('Color Scheme', 'cfb-calculator'); ?>
                                        </label>
                                        <select id="cfb_pdf_color_scheme" name="cfb_pdf_color_scheme" class="cfb-setting-select">
                                            <option value="blue" <?php selected(get_option('cfb_pdf_color_scheme', 'blue'), 'blue'); ?>>
                                                <?php _e('Professional Blue', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="green" <?php selected(get_option('cfb_pdf_color_scheme', 'blue'), 'green'); ?>>
                                                <?php _e('Business Green', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="gray" <?php selected(get_option('cfb_pdf_color_scheme', 'blue'), 'gray'); ?>>
                                                <?php _e('Corporate Gray', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="custom" <?php selected(get_option('cfb_pdf_color_scheme', 'blue'), 'custom'); ?>>
                                                <?php _e('Custom Colors', 'cfb-calculator'); ?>
                                            </option>
                                        </select>
                                        <p class="cfb-setting-description"><?php _e('Color scheme for PDF design', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label class="cfb-setting-label">
                                            <input type="checkbox"
                                                   id="cfb_pdf_rtl_support"
                                                   name="cfb_pdf_rtl_support"
                                                   value="1"
                                                   <?php checked(get_option('cfb_pdf_rtl_support', 0), 1); ?>>
                                            <span class="dashicons dashicons-translation"></span>
                                            <?php _e('RTL Support', 'cfb-calculator'); ?>
                                        </label>
                                        <p class="cfb-setting-description"><?php _e('Enable Right-to-Left layout for Arabic, Hebrew, Farsi languages', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label class="cfb-setting-label">
                                            <input type="checkbox"
                                                   id="cfb_convert_to_farsi_digits"
                                                   name="cfb_convert_to_farsi_digits"
                                                   value="1"
                                                   <?php checked(get_option('cfb_convert_to_farsi_digits', 0), 1); ?>>
                                            <span class="dashicons dashicons-calculator"></span>
                                            <?php _e('Convert to Farsi Digits', 'cfb-calculator'); ?>
                                        </label>
                                        <p class="cfb-setting-description"><?php _e('Convert all numbers to Farsi/Persian digits (۰۱۲۳۴۵۶۷۸۹)', 'cfb-calculator'); ?></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced PDF Layout Settings -->
                            <div class="cfb-settings-section">
                                <h3 class="cfb-section-title">
                                    <span class="dashicons dashicons-admin-customizer"></span>
                                    <?php _e('PDF Layout & Typography', 'cfb-calculator'); ?>
                                </h3>

                                <div class="cfb-settings-grid">
                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_total_position" class="cfb-setting-label">
                                            <?php _e('Total Position', 'cfb-calculator'); ?>
                                        </label>
                                        <select id="cfb_pdf_total_position" name="cfb_pdf_total_position" class="cfb-setting-select">
                                            <option value="left" <?php selected(get_option('cfb_pdf_total_position', 'right'), 'left'); ?>>
                                                <?php _e('Left', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="center" <?php selected(get_option('cfb_pdf_total_position', 'right'), 'center'); ?>>
                                                <?php _e('Center', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="right" <?php selected(get_option('cfb_pdf_total_position', 'right'), 'right'); ?>>
                                                <?php _e('Right', 'cfb-calculator'); ?>
                                            </option>
                                        </select>
                                        <p class="cfb-setting-description"><?php _e('Position of the total amount section in PDF', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_title_font_size" class="cfb-setting-label">
                                            <?php _e('Title Font Size', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="number"
                                               id="cfb_pdf_title_font_size"
                                               name="cfb_pdf_title_font_size"
                                               value="<?php echo esc_attr(get_option('cfb_pdf_title_font_size', 24)); ?>"
                                               class="cfb-setting-input"
                                               min="12"
                                               max="48"
                                               step="1">
                                        <p class="cfb-setting-description"><?php _e('Font size for invoice title (12-48px)', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_billto_font_size" class="cfb-setting-label">
                                            <?php _e('Bill To Font Size', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="number"
                                               id="cfb_pdf_billto_font_size"
                                               name="cfb_pdf_billto_font_size"
                                               value="<?php echo esc_attr(get_option('cfb_pdf_billto_font_size', 12)); ?>"
                                               class="cfb-setting-input"
                                               min="8"
                                               max="24"
                                               step="1">
                                        <p class="cfb-setting-description"><?php _e('Font size for Bill To section (8-24px)', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_payment_font_size" class="cfb-setting-label">
                                            <?php _e('Payment Terms Font Size', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="number"
                                               id="cfb_pdf_payment_font_size"
                                               name="cfb_pdf_payment_font_size"
                                               value="<?php echo esc_attr(get_option('cfb_pdf_payment_font_size', 10)); ?>"
                                               class="cfb-setting-input"
                                               min="8"
                                               max="18"
                                               step="1">
                                        <p class="cfb-setting-description"><?php _e('Font size for payment terms section (8-18px)', 'cfb-calculator'); ?></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Tax Configuration Section -->
                            <div class="cfb-settings-section">
                                <h3 class="cfb-section-title">
                                    <span class="dashicons dashicons-money-alt"></span>
                                    <?php _e('Tax Configuration', 'cfb-calculator'); ?>
                                </h3>

                                <div class="cfb-settings-grid">
                                    <div class="cfb-setting-item">
                                        <label class="cfb-setting-label">
                                            <input type="checkbox"
                                                   id="cfb_pdf_tax_enabled"
                                                   name="cfb_pdf_tax_enabled"
                                                   value="1"
                                                   <?php checked(get_option('cfb_pdf_tax_enabled', 0), 1); ?>>
                                            <span class="dashicons dashicons-plus-alt"></span>
                                            <?php _e('Enable Tax Calculation', 'cfb-calculator'); ?>
                                        </label>
                                        <p class="cfb-setting-description"><?php _e('Add tax to invoice totals', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_tax_rate" class="cfb-setting-label">
                                            <?php _e('Tax Rate (%)', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="number"
                                               id="cfb_pdf_tax_rate"
                                               name="cfb_pdf_tax_rate"
                                               value="<?php echo esc_attr(get_option('cfb_pdf_tax_rate', 10)); ?>"
                                               class="cfb-setting-input"
                                               min="0"
                                               max="100"
                                               step="0.01"
                                               placeholder="10">
                                        <p class="cfb-setting-description"><?php _e('Tax percentage to add to subtotal (e.g., 10 for 10%)', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_tax_title" class="cfb-setting-label">
                                            <?php _e('Tax Label', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="text"
                                               id="cfb_pdf_tax_title"
                                               name="cfb_pdf_tax_title"
                                               value="<?php echo esc_attr(get_option('cfb_pdf_tax_title', __('Tax', 'cfb-calculator'))); ?>"
                                               class="cfb-setting-input"
                                               placeholder="<?php _e('Tax', 'cfb-calculator'); ?>">
                                        <p class="cfb-setting-description"><?php _e('Label for tax line in invoice (e.g., "VAT", "Sales Tax", "مالیات")', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_payment_label" class="cfb-setting-label">
                                            <?php _e('Payment Terms Label', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="text"
                                               id="cfb_pdf_payment_label"
                                               name="cfb_pdf_payment_label"
                                               value="<?php echo esc_attr(get_option('cfb_pdf_payment_label', __('Notes', 'cfb-calculator'))); ?>"
                                               class="cfb-setting-input"
                                               placeholder="<?php _e('Notes', 'cfb-calculator'); ?>">
                                        <p class="cfb-setting-description"><?php _e('Label for payment terms section (e.g., "How to Order", "Payment Conditions", "Notes")', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_yes_label" class="cfb-setting-label">
                                            <?php _e('Yes Label for Radio/Checkbox', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="text"
                                               id="cfb_pdf_yes_label"
                                               name="cfb_pdf_yes_label"
                                               value="<?php echo esc_attr(get_option('cfb_pdf_yes_label', __('Yes', 'cfb-calculator'))); ?>"
                                               class="cfb-setting-input"
                                               placeholder="<?php _e('Yes', 'cfb-calculator'); ?>">
                                        <p class="cfb-setting-description"><?php _e('Label to show when radio button or checkbox value is 1', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_pdf_no_label" class="cfb-setting-label">
                                            <?php _e('No Label for Radio/Checkbox', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="text"
                                               id="cfb_pdf_no_label"
                                               name="cfb_pdf_no_label"
                                               value="<?php echo esc_attr(get_option('cfb_pdf_no_label', __('No', 'cfb-calculator'))); ?>"
                                               class="cfb-setting-input"
                                               placeholder="<?php _e('No', 'cfb-calculator'); ?>">
                                        <p class="cfb-setting-description"><?php _e('Label to show when radio button or checkbox value is 0', 'cfb-calculator'); ?></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Invoice Configuration Section -->
                            <div class="cfb-settings-section">
                                <h3 class="cfb-section-title">
                                    <span class="dashicons dashicons-media-document"></span>
                                    <?php _e('Invoice Configuration', 'cfb-calculator'); ?>
                                </h3>

                                <div class="cfb-settings-grid">
                                    <div class="cfb-setting-item">
                                        <label for="cfb_invoice_title" class="cfb-setting-label">
                                            <?php _e('Invoice Title', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="text"
                                               id="cfb_invoice_title"
                                               name="cfb_invoice_title"
                                               value="<?php echo esc_attr(get_option('cfb_invoice_title', __('INVOICE', 'cfb-calculator'))); ?>"
                                               class="cfb-setting-input"
                                               placeholder="<?php _e('INVOICE', 'cfb-calculator'); ?>">
                                        <p class="cfb-setting-description"><?php _e('Title text to display on invoices (e.g., INVOICE, فاکتور, BILL)', 'cfb-calculator'); ?></p>
                                    </div>

                                    <div class="cfb-setting-item">
                                        <label for="cfb_invoice_prefix" class="cfb-setting-label">
                                            <?php _e('Invoice Number Prefix', 'cfb-calculator'); ?>
                                        </label>
                                        <input type="text"
                                               id="cfb_invoice_prefix"
                                               name="cfb_invoice_prefix"
                                               value="<?php echo esc_attr(get_option('cfb_invoice_prefix', 'INV')); ?>"
                                               class="cfb-setting-input"
                                               maxlength="10">
                                        <p class="cfb-setting-description"><?php _e('Prefix for invoice numbers (e.g., INV-202401-0001)', 'cfb-calculator'); ?></p>
                                    </div>
                                </div>

                                <div class="cfb-setting-item cfb-full-width">
                                    <label for="cfb_payment_terms" class="cfb-setting-label">
                                        <?php _e('Payment Terms', 'cfb-calculator'); ?>
                                    </label>
                                    <textarea id="cfb_payment_terms"
                                              name="cfb_payment_terms"
                                              rows="3"
                                              class="cfb-setting-textarea"><?php echo esc_textarea(get_option('cfb_payment_terms', 'Payment is due within 30 days.')); ?></textarea>
                                    <p class="cfb-setting-description"><?php _e('Default payment terms to appear on invoices', 'cfb-calculator'); ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Settings -->
                        <div class="cfb-tab-content" id="advanced-tab">
                            <h2><?php _e('Advanced Settings', 'cfb-calculator'); ?></h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_enable_caching"><?php _e('Enable Caching', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" 
                                                   id="cfb_enable_caching" 
                                                   name="cfb_enable_caching" 
                                                   value="1" 
                                                   <?php checked(get_option('cfb_enable_caching', 1), 1); ?>>
                                            <?php _e('Cache calculation results for better performance', 'cfb-calculator'); ?>
                                        </label>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_debug_mode"><?php _e('Debug Mode', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" 
                                                   id="cfb_debug_mode" 
                                                   name="cfb_debug_mode" 
                                                   value="1" 
                                                   <?php checked(get_option('cfb_debug_mode', 0), 1); ?>>
                                            <?php _e('Enable debug mode for troubleshooting', 'cfb-calculator'); ?>
                                        </label>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_custom_css"><?php _e('Custom CSS', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <textarea id="cfb_custom_css" 
                                                  name="cfb_custom_css" 
                                                  rows="10" 
                                                  cols="50" 
                                                  class="large-text code"><?php echo esc_textarea(get_option('cfb_custom_css', '')); ?></textarea>
                                        <p class="description"><?php _e('Add custom CSS to style your calculator forms', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <p class="submit">
                            <button type="submit" class="button-primary"><?php _e('Save Settings', 'cfb-calculator'); ?></button>
                            <button type="button" class="button" id="cfb-reset-settings"><?php _e('Reset to Defaults', 'cfb-calculator'); ?></button>
                        </p>
                    </form>
                </div>
            </div>
        </div>
        
        <style>
        .cfb-settings-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .cfb-settings-nav {
            flex: 0 0 200px;
        }
        .cfb-tabs {
            list-style: none;
            margin: 0;
            padding: 0;
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
        }
        .cfb-tab {
            border-bottom: 1px solid #ccd0d4;
            cursor: pointer;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: background-color 0.2s;
        }
        .cfb-tab:last-child {
            border-bottom: none;
        }
        .cfb-tab:hover {
            background: #f6f7f7;
        }
        .cfb-tab.active {
            background: #0073aa;
            color: #fff;
        }
        .cfb-settings-content {
            flex: 1;
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
        }
        .cfb-tab-content {
            display: none;
        }
        .cfb-tab-content.active {
            display: block;
        }

        /* Enhanced PDF Settings Styles */
        .cfb-settings-section {
            background: #f9f9f9;
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .cfb-section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 0 0 20px 0;
            font-size: 16px;
            font-weight: 600;
            color: #23282d;
            padding-bottom: 10px;
            border-bottom: 2px solid #0073aa;
        }

        .cfb-section-title .dashicons {
            color: #0073aa;
            font-size: 20px;
        }

        .cfb-settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .cfb-setting-item {
            display: flex;
            flex-direction: column;
        }

        .cfb-setting-item.cfb-full-width {
            grid-column: 1 / -1;
        }

        .cfb-setting-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #23282d;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .cfb-setting-input,
        .cfb-setting-select,
        .cfb-setting-textarea {
            padding: 10px 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .cfb-setting-input:focus,
        .cfb-setting-select:focus,
        .cfb-setting-textarea:focus {
            outline: none;
            border-color: #0073aa;
            box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
        }

        .cfb-setting-description {
            margin: 6px 0 0 0;
            font-size: 13px;
            color: #646970;
            font-style: italic;
        }

        /* Logo Upload Styles */
        .cfb-logo-upload-container {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .cfb-logo-preview {
            flex: 0 0 200px;
            height: 120px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;
            overflow: hidden;
        }

        .cfb-logo-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .cfb-logo-placeholder {
            text-align: center;
            color: #646970;
        }

        .cfb-logo-placeholder .dashicons {
            font-size: 32px;
            margin-bottom: 8px;
            opacity: 0.5;
        }

        .cfb-logo-placeholder p {
            margin: 0;
            font-size: 13px;
        }

        .cfb-logo-controls {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .cfb-upload-logo-btn,
        .cfb-remove-logo-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .cfb-upload-logo-btn {
            background: #0073aa;
            color: #fff;
            border: none;
        }

        .cfb-upload-logo-btn:hover {
            background: #005a87;
            transform: translateY(-1px);
        }

        .cfb-remove-logo-btn {
            background: #dc3232;
            color: #fff;
            border: none;
        }

        .cfb-remove-logo-btn:hover {
            background: #a02622;
        }

        /* Template Selector Styles */
        .cfb-template-selector {
            margin-top: 10px;
        }

        .cfb-template-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .cfb-template-option {
            cursor: pointer;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            background: #fff;
        }

        .cfb-template-option:hover {
            border-color: #0073aa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 115, 170, 0.15);
        }

        .cfb-template-option.selected {
            border-color: #0073aa;
            background: #f0f8ff;
        }

        .cfb-template-option input[type="radio"] {
            display: none;
        }

        .cfb-template-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .cfb-template-thumbnail {
            width: 80px;
            height: 100px;
            border: 1px solid #ddd;
            border-radius: 4px;
            position: relative;
            background: #f9f9f9;
        }

        .cfb-template-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .cfb-template-classic {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .cfb-template-minimal {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .cfb-template-name {
            font-weight: 600;
            font-size: 13px;
            color: #23282d;
        }

        /* RTL Support */
        .cfb-settings-page[dir="rtl"] .cfb-settings-container {
            direction: rtl;
        }

        .cfb-settings-page[dir="rtl"] .cfb-section-title {
            flex-direction: row-reverse;
        }

        .cfb-settings-page[dir="rtl"] .cfb-setting-label {
            flex-direction: row-reverse;
        }

        .cfb-settings-page[dir="rtl"] .cfb-logo-upload-container {
            flex-direction: row-reverse;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .cfb-settings-container {
                flex-direction: column;
            }

            .cfb-settings-nav {
                flex: none;
            }

            .cfb-tabs {
                display: flex;
                overflow-x: auto;
            }

            .cfb-tab {
                flex: 0 0 auto;
                white-space: nowrap;
            }

            .cfb-settings-grid {
                grid-template-columns: 1fr;
            }

            .cfb-template-options {
                grid-template-columns: 1fr;
            }

            .cfb-logo-upload-container {
                flex-direction: column;
            }

            .cfb-logo-preview {
                flex: none;
                width: 100%;
            }
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // Tab switching functionality
            $('.cfb-tab').on('click', function() {
                const tabId = $(this).data('tab');

                // Update active tab
                $('.cfb-tab').removeClass('active');
                $(this).addClass('active');

                // Update active content
                $('.cfb-tab-content').removeClass('active');
                $('#' + tabId + '-tab').addClass('active');
            });

            // Logo upload functionality
            let mediaUploader;

            $(document).on('click', '.cfb-upload-logo-btn', function(e) {
                e.preventDefault();

                // Check if wp.media is available
                if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
                    alert('<?php _e('Media uploader not available. Please refresh the page.', 'cfb-calculator'); ?>');
                    return;
                }

                // If the uploader object has already been created, reopen the dialog
                if (mediaUploader) {
                    mediaUploader.open();
                    return;
                }

                // Create the media uploader
                mediaUploader = wp.media({
                    title: '<?php _e('Choose Company Logo', 'cfb-calculator'); ?>',
                    button: {
                        text: '<?php _e('Use this logo', 'cfb-calculator'); ?>'
                    },
                    library: {
                        type: 'image'
                    },
                    multiple: false
                });

                // When an image is selected, run a callback
                mediaUploader.on('select', function() {
                    const attachment = mediaUploader.state().get('selection').first().toJSON();

                    console.log('Selected attachment:', attachment);

                    // Update the hidden input
                    $('#cfb_company_logo').val(attachment.url);

                    // Update the preview
                    $('.cfb-logo-preview').html('<img src="' + attachment.url + '" alt="Company Logo" class="cfb-logo-image">');

                    // Show remove button if not already visible
                    if (!$('.cfb-remove-logo-btn').length) {
                        $('.cfb-logo-controls').append(
                            '<button type="button" class="button cfb-remove-logo-btn">' +
                            '<span class="dashicons dashicons-trash"></span>' +
                            '<?php _e('Remove Logo', 'cfb-calculator'); ?>' +
                            '</button>'
                        );
                    }

                    // Trigger change event for auto-save
                    $('#cfb_company_logo').trigger('change');
                });

                // Open the uploader dialog
                mediaUploader.open();
            });

            // Logo removal functionality
            $(document).on('click', '.cfb-remove-logo-btn', function(e) {
                e.preventDefault();

                if (confirm('<?php _e('Are you sure you want to remove the logo?', 'cfb-calculator'); ?>')) {
                    // Clear the hidden input
                    $('#cfb_company_logo').val('');

                    // Reset the preview
                    $('.cfb-logo-preview').html(
                        '<div class="cfb-logo-placeholder">' +
                        '<span class="dashicons dashicons-format-image"></span>' +
                        '<p><?php _e('No logo uploaded', 'cfb-calculator'); ?></p>' +
                        '</div>'
                    );

                    // Remove the remove button
                    $(this).remove();
                }
            });

            // Template selection functionality
            $('.cfb-template-option').on('click', function() {
                $('.cfb-template-option').removeClass('selected');
                $(this).addClass('selected');
                $(this).find('input[type="radio"]').prop('checked', true);
            });

            // Form validation
            $('#cfb-settings-form').on('submit', function(e) {
                let isValid = true;
                const requiredFields = ['cfb_company_name', 'cfb_company_email'];

                requiredFields.forEach(function(fieldId) {
                    const field = $('#' + fieldId);
                    const value = field.val().trim();

                    if (!value) {
                        isValid = false;
                        field.css('border-color', '#dc3232');

                        // Show error message
                        if (!field.next('.cfb-error-message').length) {
                            field.after('<span class="cfb-error-message" style="color: #dc3232; font-size: 12px; margin-top: 4px;"><?php _e('This field is required', 'cfb-calculator'); ?></span>');
                        }
                    } else {
                        field.css('border-color', '#ddd');
                        field.next('.cfb-error-message').remove();
                    }
                });

                if (!isValid) {
                    e.preventDefault();

                    // Switch to PDF tab if errors are there
                    $('.cfb-tab[data-tab="pdf"]').click();

                    // Scroll to first error
                    $('html, body').animate({
                        scrollTop: $('.cfb-error-message').first().offset().top - 100
                    }, 500);
                }
            });

            // Real-time validation
            $('input[required], select[required], textarea[required]').on('blur', function() {
                const field = $(this);
                const value = field.val().trim();

                if (!value) {
                    field.css('border-color', '#dc3232');
                    if (!field.next('.cfb-error-message').length) {
                        field.after('<span class="cfb-error-message" style="color: #dc3232; font-size: 12px; margin-top: 4px;"><?php _e('This field is required', 'cfb-calculator'); ?></span>');
                    }
                } else {
                    field.css('border-color', '#ddd');
                    field.next('.cfb-error-message').remove();
                }
            });

            // RTL detection and adjustment
            function adjustForRTL() {
                const isRTL = $('html').attr('dir') === 'rtl' || $('body').hasClass('rtl');

                if (isRTL) {
                    $('.cfb-settings-page').attr('dir', 'rtl');
                }
            }

            adjustForRTL();

            // Color scheme preview
            $('#cfb_pdf_color_scheme').on('change', function() {
                const scheme = $(this).val();
                const preview = $('.cfb-template-thumbnail');

                // Update template previews based on color scheme
                switch(scheme) {
                    case 'blue':
                        $('.cfb-template-modern').css('background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
                        break;
                    case 'green':
                        $('.cfb-template-modern').css('background', 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)');
                        break;
                    case 'gray':
                        $('.cfb-template-modern').css('background', 'linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%)');
                        break;
                }
            });

            // Auto-save functionality (optional)
            let autoSaveTimeout;
            $('.cfb-setting-input, .cfb-setting-select, .cfb-setting-textarea').on('input change', function() {
                clearTimeout(autoSaveTimeout);

                // Show saving indicator
                if (!$('.cfb-auto-save-indicator').length) {
                    $('.submit').prepend('<span class="cfb-auto-save-indicator" style="color: #646970; font-size: 12px; margin-right: 10px;"><?php _e('Changes detected...', 'cfb-calculator'); ?></span>');
                }

                autoSaveTimeout = setTimeout(function() {
                    $('.cfb-auto-save-indicator').text('<?php _e('Ready to save', 'cfb-calculator'); ?>').css('color', '#46b450');
                }, 1000);
            });
        });
        </script>
        <?php
    }

    /**
     * Check if Persian font is available in TCPDF
     */
    private function is_persian_font_available($font_key) {
        if (!defined('K_PATH_FONTS')) {
            return false;
        }

        $font_path = K_PATH_FONTS . $font_key . '.php';
        return file_exists($font_path);
    }
    
    /**
     * AJAX handler for saving settings
     */
    public function ajax_save_settings() {
        try {
            // Check if it's a regular form submission
            if (isset($_POST['cfb_settings_nonce'])) {
                // Verify nonce for regular form
                if (!wp_verify_nonce($_POST['cfb_settings_nonce'], 'cfb_settings_nonce')) {
                    wp_send_json_error(__('Security check failed', 'cfb-calculator'));
                    return;
                }

                // Process all form fields
                $settings_fields = array(
                    'cfb_currency_symbol',
                    'cfb_currency_position',
                    'cfb_decimal_places',
                    'cfb_thousand_separator',
                    'cfb_decimal_separator',
                    'cfb_default_theme',
                    'cfb_enable_animations',
                    'cfb_auto_calculate',
                    'cfb_rtl_support',
                    'cfb_default_language',
                    'cfb_enable_caching',
                    'cfb_debug_mode',
                    'cfb_custom_css'
                );

                foreach ($settings_fields as $field) {
                    $value = isset($_POST[$field]) ? $_POST[$field] : '';

                    if ($field === 'cfb_custom_css') {
                        update_option($field, wp_kses_post($value));
                    } elseif (in_array($field, array('cfb_enable_animations', 'cfb_auto_calculate', 'cfb_rtl_support', 'cfb_enable_caching', 'cfb_debug_mode'))) {
                        update_option($field, $value ? 1 : 0);
                    } else {
                        update_option($field, sanitize_text_field($value));
                    }
                }

                // Redirect back to settings page with success message
                wp_redirect(admin_url('admin.php?page=cfb-calculator-settings&settings-updated=true'));
                exit;

            } else {
                // AJAX submission
                if (!wp_verify_nonce($_POST['nonce'], 'cfb_admin_nonce')) {
                    wp_send_json_error(__('Security check failed', 'cfb-calculator'));
                    return;
                }
            }

            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
                return;
            }

            $settings = isset($_POST['settings']) ? $_POST['settings'] : array();

            foreach ($settings as $key => $value) {
                if (in_array($key, array('cfb_custom_css'))) {
                    update_option($key, wp_kses_post($value));
                } else {
                    update_option($key, sanitize_text_field($value));
                }
            }

            wp_send_json_success(__('Settings saved successfully', 'cfb-calculator'));

        } catch (Exception $e) {
            error_log('CFB Settings save error: ' . $e->getMessage());
            wp_send_json_error(__('Error saving settings', 'cfb-calculator'));
        }
    }
    
    /**
     * Get default settings
     */
    public function get_default_settings() {
        return array(
            'cfb_currency_symbol' => '$',
            'cfb_currency_position' => 'left',
            'cfb_decimal_places' => 2,
            'cfb_thousand_separator' => ',',
            'cfb_decimal_separator' => '.',
            'cfb_default_theme' => 'modern',
            'cfb_enable_animations' => 1,
            'cfb_auto_calculate' => 1,
            'cfb_rtl_support' => 1,
            'cfb_default_language' => 'en',
            'cfb_enable_caching' => 1,
            'cfb_debug_mode' => 0,
            'cfb_custom_css' => ''
        );
    }
}
