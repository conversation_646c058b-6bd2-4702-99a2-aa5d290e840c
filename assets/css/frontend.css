/* CFB Calculator Frontend Styles - Enhanced */

/* Reset and Base Styles */
.cfb-calculator-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 900px;
    margin: 20px auto;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

.cfb-calculator-wrapper:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Loading overlay */
.cfb-calculator-wrapper.cfb-loading-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* RTL Support */
.cfb-calculator-wrapper[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

.cfb-calculator-wrapper[dir="rtl"] .cfb-field-label {
    text-align: right;
}

/* Header - Enhanced */
.cfb-calculator-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    margin: 0;
    padding: 32px 40px;
    font-size: 28px;
    font-weight: 700;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cfb-calculator-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: cfb-shine 3s infinite;
}

@keyframes cfb-shine {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.cfb-calculator-description {
    padding: 24px 40px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    color: #495057;
    text-align: center;
    font-size: 16px;
    line-height: 1.5;
}

/* Form Styles */
.cfb-calculator-form {
    padding: 30px;
}

.cfb-form-fields {
    display: grid;
    gap: 24px;
    margin-bottom: 30px;
    grid-template-columns: repeat(12, 1fr);
}

/* Field Styles */
.cfb-field {
    position: relative;
    transition: all 0.3s ease;
    grid-column: span 12; /* Default full width */
}

/* Field Width Classes - Fixed Implementation */
.cfb-field.cfb-field-width-full {
    grid-column: span 12 !important;
}

.cfb-field.cfb-field-width-half {
    grid-column: span 6 !important;
}

.cfb-field.cfb-field-width-third {
    grid-column: span 4 !important;
}

.cfb-field.cfb-field-width-quarter {
    grid-column: span 3 !important;
}

/* Responsive adjustments for field widths */
@media (max-width: 768px) {
    .cfb-field.cfb-field-width-half,
    .cfb-field.cfb-field-width-third,
    .cfb-field.cfb-field-width-quarter {
        grid-column: span 12 !important;
        width: 100% !important;
    }
}

.cfb-field.cfb-field-hidden {
    display: none;
}

.cfb-field-label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 14px;
}

.cfb-required {
    color: #e74c3c;
    margin-left: 4px;
}

.cfb-field-input {
    position: relative;
}

/* Input Styles - Enhanced */
.cfb-text-input,
.cfb-number-input,
.cfb-select-input {
    width: 100%;
    padding: 14px 18px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #fff;
    box-sizing: border-box;
    position: relative;
    font-family: inherit;
}

.cfb-text-input:hover,
.cfb-number-input:hover,
.cfb-select-input:hover {
    border-color: #ced4da;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.cfb-text-input:focus,
.cfb-number-input:focus,
.cfb-select-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
}

/* Input validation states */
.cfb-text-input.cfb-invalid,
.cfb-number-input.cfb-invalid,
.cfb-select-input.cfb-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.15);
}

.cfb-text-input.cfb-valid,
.cfb-number-input.cfb-valid,
.cfb-select-input.cfb-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.15);
}

/* Slider Styles */
.cfb-slider-container {
    padding: 10px 0;
}

.cfb-slider-input {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.cfb-slider-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.cfb-slider-input::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

.cfb-slider-input::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.cfb-slider-value {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 14px;
    color: #6c757d;
}

.cfb-slider-current {
    font-weight: 600;
    color: #667eea;
    font-size: 16px;
}

/* Radio and Checkbox Styles */
.cfb-radio-group,
.cfb-checkbox-group {
    display: grid;
    gap: 12px;
}

.cfb-radio-option,
.cfb-checkbox-option {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
}

.cfb-radio-option:hover,
.cfb-checkbox-option:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.cfb-radio-option input,
.cfb-checkbox-option input {
    margin-right: 12px;
    margin-left: 0;
    transform: scale(1.2);
    accent-color: #667eea;
}

.cfb-radio-option input:checked + .cfb-radio-label,
.cfb-checkbox-option input:checked + .cfb-checkbox-label {
    color: #667eea;
    font-weight: 600;
}

.cfb-option-price {
    margin-left: auto;
    color: #28a745;
    font-weight: 600;
}

/* RTL adjustments for radio/checkbox */
.cfb-calculator-wrapper[dir="rtl"] .cfb-radio-option input,
.cfb-calculator-wrapper[dir="rtl"] .cfb-checkbox-option input {
    margin-right: 0;
    margin-left: 12px;
}

.cfb-calculator-wrapper[dir="rtl"] .cfb-option-price {
    margin-left: 0;
    margin-right: auto;
}

/* Field Description */
.cfb-field-description {
    margin-top: 6px;
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

/* Calculation Results */
.cfb-calculation-results {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid #e9ecef;
}

.cfb-subtotals h4 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.cfb-subtotal-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.cfb-subtotal-line:last-child {
    border-bottom: none;
}

.cfb-subtotal-label {
    color: #495057;
    font-size: 14px;
}

.cfb-subtotal-value {
    font-weight: 600;
    color: #28a745;
    font-size: 14px;
}

.cfb-total-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid #667eea;
}

.cfb-total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.cfb-total-label {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
}

.cfb-total-value {
    font-size: 24px;
    font-weight: 700;
    color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Form Actions */
.cfb-form-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 24px;
}

.cfb-calculate-btn,
.cfb-reset-btn {
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cfb-calculate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.cfb-calculate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.cfb-reset-btn {
    background: #6c757d;
    color: #fff;
}

.cfb-reset-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Loading State */
.cfb-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 20px;
    color: #667eea;
    font-weight: 600;
}

.cfb-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: cfb-spin 1s linear infinite;
}

@keyframes cfb-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.cfb-error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #f5c6cb;
    margin-top: 16px;
    font-size: 14px;
}

/* Success Message */
.cfb-success-message {
    background: #d1e7dd;
    color: #0f5132;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #badbcc;
    margin-top: 16px;
    font-size: 14px;
}

/* Invoice Section */
.cfb-invoice-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin-top: 24px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.cfb-invoice-checkbox {
    margin-bottom: 20px;
}

.cfb-invoice-checkbox label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
}

.cfb-invoice-checkbox input[type="checkbox"] {
    margin-right: 12px;
    transform: scale(1.3);
    accent-color: #667eea;
}

.cfb-invoice-form {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    border: 1px solid #e9ecef;
    margin-top: 16px;
}

.cfb-invoice-form h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding-bottom: 12px;
    border-bottom: 2px solid #667eea;
}

.cfb-invoice-fields {
    display: grid;
    gap: 20px;
}

.cfb-field-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.cfb-field-col {
    display: flex;
    flex-direction: column;
}

.cfb-field-col label {
    font-weight: 600;
    margin-bottom: 6px;
    color: #2c3e50;
    font-size: 14px;
}

.cfb-field-col input,
.cfb-field-col textarea {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
    box-sizing: border-box;
}

.cfb-field-col input:focus,
.cfb-field-col textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.cfb-field-col textarea {
    resize: vertical;
    min-height: 80px;
}

.cfb-invoice-actions {
    text-align: center;
    margin-top: 20px;
}

.cfb-generate-invoice-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: #fff;
    border: none;
    padding: 14px 28px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.cfb-generate-invoice-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.cfb-generate-invoice-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

/* RTL adjustments for invoice section */
.cfb-calculator-wrapper[dir="rtl"] .cfb-invoice-checkbox input[type="checkbox"] {
    margin-right: 0;
    margin-left: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cfb-calculator-wrapper {
        margin: 0 16px;
        border-radius: 8px;
    }

    .cfb-calculator-form {
        padding: 20px;
    }

    .cfb-calculator-title {
        padding: 20px;
        font-size: 20px;
    }

    .cfb-form-actions {
        flex-direction: column;
    }

    .cfb-calculate-btn,
    .cfb-reset-btn {
        width: 100%;
    }

    .cfb-total-value {
        font-size: 20px;
    }

    /* Responsive field widths - all fields become full width on mobile */
    .cfb-field.cfb-field-width-half,
    .cfb-field.cfb-field-width-third,
    .cfb-field.cfb-field-width-quarter {
        grid-column: span 12;
    }
    
    .cfb-radio-group,
    .cfb-checkbox-group {
        gap: 8px;
    }
    
    .cfb-radio-option,
    .cfb-checkbox-option {
        padding: 10px 12px;
    }

    /* Invoice section responsive */
    .cfb-field-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .cfb-invoice-form {
        padding: 16px;
    }

    .cfb-generate-invoice-btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 14px;
    }
}

/* Animation Classes */
.cfb-fade-in {
    animation: cfb-fadeIn 0.5s ease-in-out;
}

.cfb-slide-up {
    animation: cfb-slideUp 0.5s ease-in-out;
}

@keyframes cfb-fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes cfb-slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .cfb-calculator-wrapper {
        border: 2px solid #000;
    }
    
    .cfb-text-input,
    .cfb-number-input,
    .cfb-select-input {
        border: 2px solid #000;
    }
    
    .cfb-calculate-btn {
        background: #000;
        border: 2px solid #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .cfb-calculator-wrapper,
    .cfb-field,
    .cfb-text-input,
    .cfb-number-input,
    .cfb-select-input,
    .cfb-slider-input::-webkit-slider-thumb,
    .cfb-radio-option,
    .cfb-checkbox-option,
    .cfb-calculate-btn,
    .cfb-reset-btn {
        transition: none;
    }
    
    .cfb-spinner {
        animation: none;
    }
    
    .cfb-fade-in,
    .cfb-slide-up {
        animation: none;
    }
}

/* Subtotal Fields */
.cfb-subtotal-field {
    background: #f1f8ff;
    border: 2px solid #b3d9ff;
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
}

.cfb-subtotal-breakdown {
    margin-bottom: 15px;
}

.cfb-subtotal-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e0f0ff;
}

.cfb-subtotal-item:last-child {
    border-bottom: none;
}

.cfb-subtotal-label {
    font-weight: 500;
    color: #495057;
}

.cfb-subtotal-amount {
    font-weight: bold;
    color: #007bff;
}

.cfb-subtotal-total {
    text-align: center;
    padding: 15px 0;
    border-top: 2px solid #007bff;
    margin-top: 10px;
}

.cfb-subtotal-total-label {
    color: #495057;
    margin-right: 10px;
}

.cfb-subtotal-total-amount {
    color: #007bff;
    font-size: 20px;
}

/* Hide Results Initially */
.cfb-results-hidden .cfb-calculation-field,
.cfb-results-hidden .cfb-total-section,
.cfb-results-hidden .cfb-results-section,
.cfb-results-hidden .cfb-subtotal-section,
.cfb-results-hidden .cfb-subtotal-field,
.cfb-results-hidden .cfb-calculation-results {
    display: none !important;
}

/* Show results with animation when revealed */
.cfb-calculation-field,
.cfb-total-section,
.cfb-results-section,
.cfb-subtotal-section,
.cfb-subtotal-field,
.cfb-calculation-results {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-calculation-field,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-total-section,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-results-section,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-subtotal-section,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-subtotal-field,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-calculation-results {
    opacity: 1;
    transform: translateY(0);
}

/* Conditional field hiding */
.cfb-field-hidden {
    display: none !important;
}

.cfb-field.cfb-fade-in {
    animation: cfbFadeIn 0.3s ease;
}

@keyframes cfbFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ========================================
   THEME STYLES
   ======================================== */

/* MATERIAL DESIGN THEME - Enhanced Beautiful Design */
.cfb-theme-material {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 16px 64px rgba(0, 0, 0, 0.12);
    border-radius: 16px;
    overflow: hidden;
    background: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.cfb-theme-material::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6200ee 0%, #3700b3 50%, #6200ee 100%);
    background-size: 200% 100%;
    animation: cfb-material-gradient 3s ease-in-out infinite;
}

@keyframes cfb-material-gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.cfb-theme-material .cfb-calculator-title {
    background: linear-gradient(135deg, #6200ee 0%, #3700b3 100%);
    color: #ffffff;
    padding: 32px 40px;
    font-size: 28px;
    font-weight: 400;
    text-align: center;
    letter-spacing: 0.25px;
    box-shadow: 0 4px 8px rgba(98, 0, 238, 0.3);
    position: relative;
    margin: 0;
}

.cfb-theme-material .cfb-calculator-description {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 24px 40px;
    color: #5f6368;
    font-size: 16px;
    line-height: 1.6;
    border-bottom: 1px solid #e8eaed;
    text-align: center;
    font-weight: 400;
}

.cfb-theme-material .cfb-calculator-form {
    padding: 40px;
    background: linear-gradient(135deg, #fafbff 0%, #ffffff 100%);
}

.cfb-theme-material .cfb-field-label {
    color: #5f6368;
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 12px;
    letter-spacing: 0.25px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    display: block;
}

.cfb-theme-material .cfb-text-input,
.cfb-theme-material .cfb-number-input,
.cfb-theme-material .cfb-select-input {
    border: 1px solid #dadce0;
    border-radius: 8px;
    background: #ffffff;
    padding: 16px 20px;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
    position: relative;
    height: 56px;
    box-sizing: border-box;
    font-weight: 400;
    color: #202124;
}

.cfb-theme-material .cfb-text-input:focus,
.cfb-theme-material .cfb-number-input:focus,
.cfb-theme-material .cfb-select-input:focus {
    border-color: #1a73e8;
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.15), 0 0 0 1px rgba(26, 115, 232, 0.3);
    outline: none;
    transform: translateY(-1px);
}

.cfb-theme-material .cfb-field:focus-within .cfb-field-label {
    color: #1a73e8;
    transform: translateY(-2px);
}

.cfb-theme-material .cfb-radio-option,
.cfb-theme-material .cfb-checkbox-option {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 12px;
    padding: 20px 24px;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.cfb-theme-material .cfb-radio-option:hover,
.cfb-theme-material .cfb-checkbox-option:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
    border-color: #1a73e8;
    background: #f8f9ff;
}

.cfb-theme-material .cfb-radio-option::before,
.cfb-theme-material .cfb-checkbox-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
    transform: scaleY(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0 4px 4px 0;
}

.cfb-theme-material .cfb-radio-option:hover::before,
.cfb-theme-material .cfb-checkbox-option:hover::before {
    transform: scaleY(1);
}

.cfb-theme-material .cfb-calculate-btn {
    background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
    border-radius: 24px;
    padding: 16px 32px;
    font-weight: 500;
    text-transform: none;
    letter-spacing: 0.25px;
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 16px;
    height: 48px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.cfb-theme-material .cfb-calculate-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.cfb-theme-material .cfb-calculate-btn:hover {
    background: linear-gradient(135deg, #1557b0 0%, #3367d6 100%);
    box-shadow: 0 4px 16px rgba(26, 115, 232, 0.4);
    transform: translateY(-2px);
}

.cfb-theme-material .cfb-calculate-btn:hover::before {
    width: 300px;
    height: 300px;
}

.cfb-theme-material .cfb-reset-btn {
    background: transparent;
    border: 2px solid #dadce0;
    color: #5f6368;
    border-radius: 24px;
    padding: 14px 28px;
    font-weight: 500;
    font-size: 16px;
    text-transform: none;
    letter-spacing: 0.25px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 48px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
}

.cfb-theme-material .cfb-reset-btn:hover {
    background: #f8f9fa;
    border-color: #1a73e8;
    color: #1a73e8;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.15);
}

.cfb-theme-material .cfb-calculation-results {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaed;
    padding: 32px;
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
}

.cfb-theme-material .cfb-calculation-results::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1a73e8 0%, #4285f4 100%);
}

.cfb-theme-material .cfb-total-value {
    color: #1a73e8;
    background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 500;
}

.cfb-theme-material .cfb-calculation-field {
    background: #ffffff;
    border-radius: 8px;
    padding: 16px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    margin: 16px 0;
}

.cfb-theme-material .cfb-calculation-value {
    font-size: 18px;
    font-weight: 500;
    color: #1976d2;
}

.cfb-theme-material .cfb-slider-input {
    background: #e0e0e0;
    height: 6px;
}

.cfb-theme-material .cfb-slider-input::-webkit-slider-thumb {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    width: 20px;
    height: 20px;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

/* MINIMAL THEME - Enhanced Clean Design */
.cfb-theme-minimal {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.2s ease;
    position: relative;
}

.cfb-theme-minimal:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #d1d5db;
}

.cfb-theme-minimal .cfb-calculator-title {
    background: #ffffff;
    color: #111827;
    padding: 32px 32px;
    font-size: 24px;
    font-weight: 700;
    text-align: center;
    border-bottom: 1px solid #f3f4f6;
    margin: 0;
    letter-spacing: -0.025em;
    position: relative;
}

.cfb-theme-minimal .cfb-calculator-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: #111827;
}

.cfb-theme-minimal .cfb-calculator-description {
    background: #f9fafb;
    padding: 20px 32px;
    color: #6b7280;
    font-size: 15px;
    line-height: 1.6;
    border-bottom: 1px solid #f3f4f6;
    text-align: center;
    font-weight: 400;
}

.cfb-theme-minimal .cfb-calculator-form {
    padding: 32px;
    background: #ffffff;
}

.cfb-theme-minimal .cfb-field-label {
    color: #374151;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 8px;
    letter-spacing: -0.025em;
    transition: all 0.2s ease;
}

.cfb-theme-minimal .cfb-text-input,
.cfb-theme-minimal .cfb-number-input,
.cfb-theme-minimal .cfb-select-input {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: #ffffff;
    padding: 12px 16px;
    font-size: 15px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
    height: 48px;
    box-sizing: border-box;
    font-weight: 400;
    color: #111827;
}

.cfb-theme-minimal .cfb-text-input:focus,
.cfb-theme-minimal .cfb-number-input:focus,
.cfb-theme-minimal .cfb-select-input:focus {
    border-color: #111827;
    box-shadow: 0 0 0 3px rgba(17, 24, 39, 0.1);
    outline: none;
    background: #fafafa;
}

.cfb-theme-minimal .cfb-field:focus-within .cfb-field-label {
    color: #111827;
    transform: translateY(-1px);
}

.cfb-theme-minimal .cfb-radio-option,
.cfb-theme-minimal .cfb-checkbox-option {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    position: relative;
}

.cfb-theme-minimal .cfb-radio-option:hover,
.cfb-theme-minimal .cfb-checkbox-option:hover {
    border-color: #111827;
    background: #f9fafb;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.cfb-theme-minimal .cfb-radio-label,
.cfb-theme-minimal .cfb-checkbox-label {
    font-size: 15px;
    padding-left: 12px;
    font-weight: 400;
    color: #374151;
}

.cfb-theme-minimal .cfb-calculate-btn {
    background: #111827;
    color: #ffffff;
    border: none;
    border-radius: 8px;
    padding: 14px 28px;
    font-weight: 600;
    font-size: 15px;
    text-transform: none;
    letter-spacing: -0.025em;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: 48px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.cfb-theme-minimal .cfb-calculate-btn:hover {
    background: #1f2937;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.cfb-theme-minimal .cfb-reset-btn {
    background: #ffffff;
    color: #6b7280;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 14px 28px;
    font-weight: 500;
    font-size: 15px;
    text-transform: none;
    letter-spacing: -0.025em;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
    height: 48px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
}

.cfb-theme-minimal .cfb-reset-btn:hover {
    background: #f9fafb;
    border-color: #111827;
    color: #111827;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.cfb-theme-minimal .cfb-calculation-results {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 24px;
    margin-bottom: 24px;
    position: relative;
}

.cfb-theme-minimal .cfb-calculation-results::before {
    content: '';
    position: absolute;
    top: 0;
    left: 24px;
    right: 24px;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #111827 50%, transparent 100%);
}

.cfb-theme-minimal .cfb-total-value {
    color: #111827;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    font-weight: 700;
    letter-spacing: -0.025em;
}

/* PROFESSIONAL CORPORATE THEME - Enhanced Business Design */
.cfb-theme-modern {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
}

.cfb-theme-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1e40af 0%, #3b82f6 50%, #1e40af 100%);
    background-size: 200% 100%;
    animation: cfb-corporate-gradient 4s ease-in-out infinite;
}

@keyframes cfb-corporate-gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.cfb-theme-modern .cfb-calculator-title {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: #ffffff;
    padding: 32px 40px;
    font-size: 26px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 0.5px;
    position: relative;
    margin-top: 0;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.2);
}

.cfb-theme-modern .cfb-calculator-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #1e40af;
}

.cfb-theme-modern .cfb-calculator-description {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    padding: 24px 40px;
    color: #475569;
    font-size: 16px;
    line-height: 1.6;
    border-bottom: 1px solid #e2e8f0;
    text-align: center;
    font-weight: 400;
}

.cfb-theme-modern .cfb-calculator-form {
    padding: 40px;
    background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
}

.cfb-theme-modern .cfb-field-label {
    color: #1e293b;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 10px;
    letter-spacing: 0.25px;
    position: relative;
    text-transform: uppercase;
    font-size: 12px;
}

.cfb-theme-modern .cfb-field-label::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 32px;
    height: 2px;
    background: linear-gradient(90deg, #1e40af 0%, #3b82f6 100%);
    border-radius: 1px;
}

.cfb-theme-modern .cfb-text-input,
.cfb-theme-modern .cfb-number-input,
.cfb-theme-modern .cfb-select-input {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: #ffffff;
    padding: 14px 18px;
    font-size: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    font-weight: 400;
    height: 52px;
    box-sizing: border-box;
    color: #1e293b;
}

.cfb-theme-modern .cfb-text-input:focus,
.cfb-theme-modern .cfb-number-input:focus,
.cfb-theme-modern .cfb-select-input:focus {
    border-color: #1e40af;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.15), 0 0 0 3px rgba(30, 64, 175, 0.1);
    outline: none;
    transform: translateY(-1px);
    background: #fafbff;
}

.cfb-theme-modern .cfb-field:focus-within .cfb-field-label {
    color: #1e40af;
    transform: translateY(-2px);
}

.cfb-theme-modern .cfb-radio-option,
.cfb-theme-modern .cfb-checkbox-option {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 18px 22px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.cfb-theme-modern .cfb-radio-option::before,
.cfb-theme-modern .cfb-checkbox-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
    border-radius: 0 4px 4px 0;
}

.cfb-theme-modern .cfb-radio-option::after,
.cfb-theme-modern .cfb-checkbox-option::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: #1e40af;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cfb-theme-modern .cfb-radio-option:hover,
.cfb-theme-modern .cfb-checkbox-option:hover {
    border-color: #1e40af;
    box-shadow: 0 4px 16px rgba(30, 64, 175, 0.12);
    transform: translateY(-2px);
    background: #fafbff;
}

.cfb-theme-modern .cfb-radio-label,
.cfb-theme-modern .cfb-checkbox-label {
    font-size: 15px;
    padding-left: 12px;
    font-weight: 500;
    color: #475569;
}

.cfb-theme-modern .cfb-radio-option:hover::before,
.cfb-theme-modern .cfb-checkbox-option:hover::before {
    transform: scaleY(1);
}

.cfb-theme-modern .cfb-radio-option:hover::after,
.cfb-theme-modern .cfb-checkbox-option:hover::after {
    opacity: 1;
}

.cfb-theme-modern .cfb-calculate-btn {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: #ffffff;
    border: none;
    border-radius: 10px;
    padding: 16px 32px;
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(30, 64, 175, 0.3);
    position: relative;
    overflow: hidden;
    height: 52px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;
    font-size: 14px;
}

.cfb-theme-modern .cfb-calculate-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cfb-theme-modern .cfb-calculate-btn::after {
    content: '→';
    position: absolute;
    right: 16px;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    font-size: 18px;
}

.cfb-theme-modern .cfb-calculate-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    box-shadow: 0 6px 24px rgba(30, 64, 175, 0.4);
    transform: translateY(-3px);
    padding-right: 48px;
}

.cfb-theme-modern .cfb-calculate-btn:hover::before {
    left: 100%;
}

.cfb-theme-modern .cfb-calculate-btn:hover::after {
    opacity: 1;
    transform: translateX(0);
}

.cfb-theme-modern .cfb-reset-btn {
    background: #ffffff;
    color: #475569;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 14px 28px;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    height: 52px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
}

.cfb-theme-modern .cfb-reset-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 64, 175, 0.05), transparent);
    transition: left 0.5s ease;
}

.cfb-theme-modern .cfb-reset-btn:hover {
    background: #f8fafc;
    border-color: #1e40af;
    color: #1e40af;
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.1);
}

.cfb-theme-modern .cfb-reset-btn:hover::before {
    left: 100%;
}

.cfb-theme-modern .cfb-calculation-results {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    position: relative;
    margin-bottom: 24px;
    overflow: hidden;
}

.cfb-theme-modern .cfb-calculation-results::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1e40af 0%, #3b82f6 100%);
    border-radius: 12px 12px 0 0;
}

.cfb-theme-modern .cfb-calculation-results::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    border-radius: 50%;
    opacity: 0.5;
}

.cfb-theme-modern .cfb-total-value {
    color: #1e40af;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(30, 64, 175, 0.1);
    position: relative;
}

/* ========================================
   THEME RESPONSIVE STYLES
   ======================================== */

@media (max-width: 768px) {
    /* Material Theme Mobile */
    .cfb-theme-material .cfb-calculator-title {
        padding: 24px 20px;
        font-size: 22px;
    }

    .cfb-theme-material .cfb-calculator-form {
        padding: 24px 20px;
    }

    .cfb-theme-material .cfb-calculate-btn,
    .cfb-theme-material .cfb-reset-btn {
        width: 100%;
        margin-bottom: 12px;
        padding: 14px 24px;
        border-radius: 20px;
    }

    /* Minimal Theme Mobile */
    .cfb-theme-minimal .cfb-calculator-title {
        padding: 20px 24px;
        font-size: 20px;
    }

    .cfb-theme-minimal .cfb-calculator-form {
        padding: 24px 20px;
    }

    .cfb-theme-minimal .cfb-calculate-btn,
    .cfb-theme-minimal .cfb-reset-btn {
        width: 100%;
        margin-bottom: 12px;
    }

    /* Modern Theme Mobile */
    .cfb-theme-modern .cfb-calculator-title {
        padding: 24px 20px;
        font-size: 22px;
    }

    .cfb-theme-modern .cfb-calculator-form {
        padding: 28px 20px;
    }

    .cfb-theme-modern .cfb-calculate-btn,
    .cfb-theme-modern .cfb-reset-btn {
        width: 100%;
        margin-bottom: 12px;
        padding: 14px 28px;
    }
}

/* ========================================
   RTL SUPPORT FOR THEMES
   ======================================== */

/* Material Theme RTL */
.cfb-theme-material[dir="rtl"] .cfb-field-label {
    text-align: right;
}

.cfb-theme-material[dir="rtl"] .cfb-text-input,
.cfb-theme-material[dir="rtl"] .cfb-number-input,
.cfb-theme-material[dir="rtl"] .cfb-select-input {
    text-align: right;
    direction: rtl;
}

/* Minimal Theme RTL */
.cfb-theme-minimal[dir="rtl"] .cfb-field-label {
    text-align: right;
}

.cfb-theme-minimal[dir="rtl"] .cfb-text-input,
.cfb-theme-minimal[dir="rtl"] .cfb-number-input,
.cfb-theme-minimal[dir="rtl"] .cfb-select-input {
    text-align: right;
    direction: rtl;
}

/* Modern Theme RTL */
.cfb-theme-modern[dir="rtl"] .cfb-field-label {
    text-align: right;
}

.cfb-theme-modern[dir="rtl"] .cfb-field-label::after {
    left: auto;
    right: 0;
}

.cfb-theme-modern[dir="rtl"] .cfb-text-input,
.cfb-theme-modern[dir="rtl"] .cfb-number-input,
.cfb-theme-modern[dir="rtl"] .cfb-select-input {
    text-align: right;
    direction: rtl;
}

.cfb-theme-modern[dir="rtl"] .cfb-radio-option::before,
.cfb-theme-modern[dir="rtl"] .cfb-checkbox-option::before {
    left: auto;
    right: 0;
}
