/**
 * CFB Calculator Frontend JavaScript
 * Handles form interactions, calculations, and conditional logic
 */

(function($) {
    'use strict';

    class CFBCalculator {
        constructor(wrapper) {
            this.wrapper = $(wrapper);
            this.form = this.wrapper.find('.cfb-calculator-form');
            this.formId = this.wrapper.data('form-id');
            this.fields = {};
            this.conditionalFields = [];
            this.autoCalculate = cfb_ajax.auto_calculate !== undefined ? cfb_ajax.auto_calculate : true;
            
            this.init();
        }

        init() {
            this.bindEvents();
            this.initializeFields();
            this.setupConditionalLogic();
            this.setupSliders();
            this.hideResultsInitially();

            // Don't auto-calculate on init - wait for user interaction
            // if (this.autoCalculate) {
            //     this.calculate();
            // }
        }

        bindEvents() {
            // Calculate button
            this.wrapper.on('click', '.cfb-calculate-btn', (e) => {
                e.preventDefault();
                this.calculate();
            });

            // Reset button
            this.wrapper.on('click', '.cfb-reset-btn', (e) => {
                e.preventDefault();
                this.resetForm();
            });

            // Auto-calculate on field changes
            if (this.autoCalculate) {
                this.form.on('change input', 'input, select, textarea', () => {
                    this.debounce(() => {
                        this.checkConditionalLogic();
                        this.calculate();
                    }, 300);
                });
            } else {
                this.form.on('change input', 'input, select, textarea', () => {
                    this.checkConditionalLogic();
                });
            }

            // Slider value display
            this.form.on('input', '.cfb-slider-input', (e) => {
                const slider = $(e.target);
                const valueDisplay = slider.siblings('.cfb-slider-value').find('.cfb-slider-current');
                valueDisplay.text(slider.val());
            });

            // Invoice checkbox toggle
            this.wrapper.on('change', '#cfb-want-invoice', (e) => {
                const invoiceForm = this.wrapper.find('.cfb-invoice-form');
                if ($(e.target).is(':checked')) {
                    invoiceForm.slideDown(300);
                } else {
                    invoiceForm.slideUp(300);
                }
            });

            // Generate invoice button
            this.wrapper.on('click', '.cfb-generate-invoice-btn', (e) => {
                e.preventDefault();
                this.generateInvoice();
            });
        }

        initializeFields() {
            this.form.find('[name]').each((index, element) => {
                const field = $(element);
                const name = field.attr('name').replace('[]', '');
                
                if (!this.fields[name]) {
                    this.fields[name] = {
                        element: field,
                        type: this.getFieldType(field),
                        wrapper: field.closest('.cfb-field')
                    };
                }
            });
        }

        getFieldType(field) {
            if (field.is('input[type="text"]')) return 'text';
            if (field.is('input[type="number"]')) return 'number';
            if (field.is('input[type="range"]')) return 'slider';
            if (field.is('select')) return 'dropdown';
            if (field.is('input[type="radio"]')) return 'radio';
            if (field.is('input[type="checkbox"]')) return 'checkbox';
            return 'unknown';
        }

        setupConditionalLogic() {
            this.form.find('[data-conditional]').each((index, element) => {
                const field = $(element);
                let conditionalData = field.data('conditional');

                // Parse JSON if it's a string
                if (typeof conditionalData === 'string') {
                    try {
                        conditionalData = JSON.parse(conditionalData);
                    } catch (e) {
                        console.error('Error parsing conditional data:', e);
                        return;
                    }
                }

                if (conditionalData && conditionalData.enabled) {
                    this.conditionalFields.push({
                        field: field,
                        logic: conditionalData
                    });
                }
            });

            this.checkConditionalLogic();
        }

        checkConditionalLogic() {
            this.conditionalFields.forEach(item => {
                const isVisible = this.evaluateConditionalLogic(item.logic);
                
                if (isVisible) {
                    item.field.removeClass('cfb-field-hidden').addClass('cfb-fade-in');
                } else {
                    item.field.addClass('cfb-field-hidden').removeClass('cfb-fade-in');
                }
            });
        }

        evaluateConditionalLogic(logic) {
            // If no conditions, show the field
            if (!logic.conditions || logic.conditions.length === 0) {
                return true;
            }

            let conditionsMet = 0;

            logic.conditions.forEach(condition => {
                const fieldValue = this.getFieldValue(condition.field);
                let conditionMet = false;

                switch (condition.operator) {
                    case 'equals':
                        conditionMet = (fieldValue == condition.value);
                        break;
                    case 'not_equals':
                        conditionMet = (fieldValue != condition.value);
                        break;
                    case 'greater_than':
                        conditionMet = (parseFloat(fieldValue) > parseFloat(condition.value));
                        break;
                    case 'less_than':
                        conditionMet = (parseFloat(fieldValue) < parseFloat(condition.value));
                        break;
                    case 'contains':
                        if (Array.isArray(fieldValue)) {
                            conditionMet = fieldValue.includes(condition.value);
                        } else {
                            conditionMet = (fieldValue.toString().indexOf(condition.value) !== -1);
                        }
                        break;
                    case 'not_empty':
                        conditionMet = (fieldValue !== '' && fieldValue !== null && fieldValue !== undefined);
                        break;
                    case 'empty':
                        conditionMet = (fieldValue === '' || fieldValue === null || fieldValue === undefined);
                        break;
                }

                if (conditionMet) {
                    conditionsMet++;
                }
            });

            if (logic.logic_type === 'all') {
                return conditionsMet === logic.conditions.length;
            } else {
                return conditionsMet > 0;
            }
        }

        getFieldValue(fieldName) {
            const field = this.form.find(`[name="${fieldName}"], [name="${fieldName}[]"]`);
            
            if (field.is('input[type="radio"]')) {
                return field.filter(':checked').val() || '';
            } else if (field.is('input[type="checkbox"]')) {
                const values = [];
                field.filter(':checked').each(function() {
                    values.push($(this).val());
                });
                return values;
            } else {
                return field.val() || '';
            }
        }

        setupSliders() {
            this.form.find('.cfb-slider-input').each(function() {
                const slider = $(this);
                const valueDisplay = slider.siblings('.cfb-slider-value').find('.cfb-slider-current');
                valueDisplay.text(slider.val());
            });
        }

        hideResultsInitially() {
            // Hide all calculation results initially
            this.wrapper.find('.cfb-calculation-field').hide();
            this.wrapper.find('.cfb-total-section').hide();
            this.wrapper.find('.cfb-results-section').hide();
            this.wrapper.find('.cfb-subtotal-section').hide();
            this.wrapper.find('.cfb-calculation-results').hide();

            // Hide any elements with calculation values
            this.wrapper.find('.cfb-calculated-amount').text('0');
            this.wrapper.find('.cfb-total-value').text('0');

            // Add a class to indicate results are hidden
            this.wrapper.addClass('cfb-results-hidden');
        }

        showResults() {
            // Show calculation results after calculation
            this.wrapper.find('.cfb-calculation-field').show();
            this.wrapper.find('.cfb-total-section').show();
            this.wrapper.find('.cfb-results-section').show();
            this.wrapper.find('.cfb-subtotal-section').show();
            this.wrapper.find('.cfb-calculation-results').show();

            // Remove the hidden class
            this.wrapper.removeClass('cfb-results-hidden');
        }

        calculate() {
            const formData = this.getFormData();
            
            this.showLoading();
            this.hideError();

            $.ajax({
                url: cfb_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfb_calculate_price',
                    nonce: cfb_ajax.nonce,
                    form_id: this.formId,
                    form_data: formData
                },
                success: (response) => {
                    this.hideLoading();

                    if (response.success) {
                        this.showResults(); // Show results before displaying them
                        this.displayResults(response.data);
                        this.updateCalculationFields(response.data);
                        this.animateResults();
                        this.showInvoiceOption(response.data); // Show invoice option after calculation
                    } else {
                        this.showError(response.data || 'Calculation failed');
                    }
                },
                error: (xhr, status, error) => {
                    this.hideLoading();
                    console.error('AJAX Error:', xhr.responseText);
                    this.showError('Network error occurred: ' + error);
                }
            });
        }

        getFormData() {
            const data = {};
            
            this.form.find('[name]').each(function() {
                const field = $(this);
                const name = field.attr('name').replace('[]', '');
                
                if (field.is('input[type="radio"]')) {
                    if (field.is(':checked')) {
                        data[name] = field.val();
                    }
                } else if (field.is('input[type="checkbox"]')) {
                    if (!data[name]) data[name] = [];
                    if (field.is(':checked')) {
                        data[name].push(field.val());
                    }
                } else {
                    data[name] = field.val();
                }
            });

            return data;
        }

        displayResults(results) {
            // Display subtotals (legacy support)
            if (results.subtotals) {
                results.subtotals.forEach((subtotal, index) => {
                    const subtotalElement = this.wrapper.find(`[data-subtotal="${index}"] .cfb-subtotal-value`);
                    const displayValue = subtotal.value === 0 ? '---' : subtotal.formatted;
                    subtotalElement.text(displayValue);
                });
            }

            // Display total
            const totalElement = this.wrapper.find('.cfb-total-value');
            const displayTotal = results.total === 0 ? '---' : results.formatted_total;
            totalElement.text(displayTotal);
        }

        updateCalculationFields(results) {
            // Update calculation, subtotal, and total fields
            if (results.calculations) {
                results.calculations.forEach((calculation) => {
                    const fieldElement = this.wrapper.find(`[data-field-name="${calculation.name}"]`);
                    if (fieldElement.length) {

                        if (calculation.type === 'subtotal') {
                            // Handle subtotal fields with breakdown
                            this.updateSubtotalField(fieldElement, calculation);
                        } else {
                            // Handle regular calculation and total fields
                            const valueElement = fieldElement.find('.cfb-calculated-amount');
                            const displayValue = calculation.value === 0 ? '---' : this.formatCalculationValue(calculation.value, calculation.display_type);
                            valueElement.text(displayValue);

                            // Update the hidden input
                            const hiddenInput = fieldElement.find('.cfb-calculation-input');
                            hiddenInput.val(calculation.value);

                            // Show breakdown if it's a total field with breakdown enabled
                            if (calculation.type === 'total' && results.calculations.length > 1) {
                                this.updateBreakdown(fieldElement, results.calculations);
                            }
                        }
                    }
                });
            }
        }

        updateSubtotalField(fieldElement, calculation) {
            // Update individual subtotal items
            if (calculation.breakdown && calculation.breakdown.length > 0) {
                calculation.breakdown.forEach((item, index) => {
                    const itemElement = fieldElement.find(`.cfb-subtotal-item:eq(${index})`);
                    if (itemElement.length) {
                        const amountElement = itemElement.find('.cfb-subtotal-amount');
                        const displayValue = item.value === 0 ? '---' : this.formatCalculationValue(item.value, calculation.display_type);
                        amountElement.text(displayValue);
                    }
                });
            }

            // Update total subtotal value
            const totalElement = fieldElement.find('.cfb-subtotal-total-amount');
            const displayValue = calculation.value === 0 ? '---' : this.formatCalculationValue(calculation.value, calculation.display_type);
            totalElement.text(displayValue);

            // Update the hidden input
            const hiddenInput = fieldElement.find('.cfb-subtotal-input');
            hiddenInput.val(calculation.value);
        }

        formatCalculationValue(value, displayType) {
            switch (displayType) {
                case 'currency':
                    return this.formatCurrency(value);
                case 'percentage':
                    return this.numberFormat(value, 2) + '%';
                case 'number':
                default:
                    return this.numberFormat(value, 2);
            }
        }

        updateBreakdown(totalFieldElement, calculations) {
            const breakdownElement = totalFieldElement.find('.cfb-breakdown-details');
            if (breakdownElement.length) {
                let breakdownHtml = '';

                calculations.forEach((calc) => {
                    if (calc.type !== 'total') {
                        breakdownHtml += `
                            <div class="cfb-breakdown-item">
                                <span class="cfb-breakdown-label">${calc.label}</span>
                                <span class="cfb-breakdown-value">${calc.formatted}</span>
                            </div>
                        `;
                    }
                });

                if (breakdownHtml) {
                    breakdownElement.html(breakdownHtml);
                    totalFieldElement.find('.cfb-calculation-breakdown').show();
                }
            }
        }

        animateResults() {
            const resultsSection = this.wrapper.find('.cfb-calculation-results');
            
            if (resultsSection.length) {
                resultsSection.addClass('cfb-slide-up');
                
                // Animate numbers counting up
                this.animateNumbers();
            }
        }

        animateNumbers() {
            this.wrapper.find('.cfb-subtotal-value, .cfb-total-value').each(function() {
                const element = $(this);
                const text = element.text();
                const number = parseFloat(text.replace(/[^0-9.-]+/g, ''));
                
                if (!isNaN(number)) {
                    element.prop('Counter', 0).animate({
                        Counter: number
                    }, {
                        duration: 1000,
                        easing: 'swing',
                        step: function(now) {
                            const formatted = cfb_calculator.formatCurrency(now);
                            element.text(formatted);
                        },
                        complete: function() {
                            element.text(text); // Ensure final value is exact
                        }
                    });
                }
            });
        }

        formatCurrency(value) {
            const symbol = cfb_ajax.currency_symbol;
            const position = cfb_ajax.currency_position;
            const decimals = parseInt(cfb_ajax.decimal_places);
            const thousandSep = cfb_ajax.thousand_separator;
            const decimalSep = cfb_ajax.decimal_separator;
            
            const formatted = this.numberFormat(value, decimals, decimalSep, thousandSep);
            
            if (position === 'right') {
                return formatted + ' ' + symbol;
            } else {
                return symbol + ' ' + formatted;
            }
        }

        numberFormat(number, decimals, decPoint, thousandsSep) {
            number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
            const n = !isFinite(+number) ? 0 : +number;
            const prec = !isFinite(+decimals) ? 0 : Math.abs(decimals);
            const sep = (typeof thousandsSep === 'undefined') ? ',' : thousandsSep;
            const dec = (typeof decPoint === 'undefined') ? '.' : decPoint;
            
            const s = (prec ? this.toFixedFix(n, prec) : '' + Math.round(n)).split('.');
            
            if (s[0].length > 3) {
                s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
            }
            
            if ((s[1] || '').length < prec) {
                s[1] = s[1] || '';
                s[1] += new Array(prec - s[1].length + 1).join('0');
            }
            
            return s.join(dec);
        }

        toFixedFix(n, prec) {
            const k = Math.pow(10, prec);
            return '' + (Math.round(n * k) / k);
        }

        resetForm() {
            this.form[0].reset();

            // Reset sliders
            this.form.find('.cfb-slider-input').each(function() {
                const slider = $(this);
                const defaultValue = slider.attr('value') || slider.attr('min') || 0;
                slider.val(defaultValue);
                slider.siblings('.cfb-slider-value').find('.cfb-slider-current').text(defaultValue);
            });

            // Clear results and hide them
            this.wrapper.find('.cfb-subtotal-value, .cfb-total-value').text('0');
            this.hideResultsInitially();

            // Check conditional logic
            this.checkConditionalLogic();

            // Hide error messages
            this.hideError();
        }

        showLoading() {
            this.wrapper.find('.cfb-loading').show();
            this.wrapper.find('.cfb-calculate-btn').prop('disabled', true);
        }

        hideLoading() {
            this.wrapper.find('.cfb-loading').hide();
            this.wrapper.find('.cfb-calculate-btn').prop('disabled', false);
        }

        showError(message) {
            const errorElement = this.wrapper.find('.cfb-error-message');
            errorElement.text(message).show();
        }

        hideError() {
            this.wrapper.find('.cfb-error-message').hide();
        }

        debounce(func, wait) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(func, wait);
        }

        showInvoiceOption(calculationData) {
            // Only show invoice option if there's a calculated total > 0
            if (calculationData.total && calculationData.total > 0) {
                this.wrapper.find('.cfb-invoice-section').slideDown(300);
                this.calculationData = calculationData; // Store for invoice generation
            }
        }

        generateInvoice() {
            console.log('CFB: Starting invoice generation');

            // Validate invoice form
            const invoiceName = this.wrapper.find('#cfb-invoice-name').val().trim();
            const invoiceEmail = this.wrapper.find('#cfb-invoice-email').val().trim();

            console.log('CFB: Invoice form data:', { invoiceName, invoiceEmail });

            if (!invoiceName || !invoiceEmail) {
                console.log('CFB: Validation failed - missing name or email');
                this.showError('Please fill in the required invoice fields (Name and Email).');
                return;
            }

            // Collect invoice data
            const invoiceData = {
                customer_name: invoiceName,
                customer_email: invoiceEmail,
                customer_phone: this.wrapper.find('#cfb-invoice-phone').val().trim(),
                customer_address: this.wrapper.find('#cfb-invoice-address').val().trim(),
                form_id: this.formId,
                subtotal: this.calculationData.total || 0,
                tax_amount: 0, // Can be calculated based on settings
                total_amount: this.calculationData.total || 0,
                form_data: this.getFormData(),
                calculation_data: this.calculationData
            };

            console.log('CFB: Invoice data to send:', invoiceData);
            console.log('CFB: AJAX config:', { url: cfb_ajax.ajax_url, nonce: cfb_ajax.nonce });

            const generateBtn = this.wrapper.find('.cfb-generate-invoice-btn');
            const originalText = generateBtn.text();

            generateBtn.prop('disabled', true).text('Generating...');

            const ajaxData = {
                action: 'cfb_save_invoice',
                nonce: cfb_ajax.nonce,
                ...invoiceData
            };

            console.log('CFB: Final AJAX data:', ajaxData);

            $.ajax({
                url: cfb_ajax.ajax_url,
                type: 'POST',
                data: ajaxData,
                success: (response) => {
                    console.log('CFB: Invoice creation success response:', response);
                    if (response.success) {
                        // Generate PDF immediately after creating invoice
                        this.generatePDF(response.data.invoice_id);
                    } else {
                        console.log('CFB: Invoice creation failed:', response.data);
                        this.showError(response.data || 'Failed to create invoice');
                        generateBtn.prop('disabled', false).text(originalText);
                    }
                },
                error: (xhr, status, error) => {
                    console.error('CFB: Invoice creation error details:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        statusCode: xhr.status
                    });
                    this.showError('Network error occurred while creating invoice: ' + error);
                    generateBtn.prop('disabled', false).text(originalText);
                }
            });
        }

        generatePDF(invoiceId) {
            const generateBtn = this.wrapper.find('.cfb-generate-invoice-btn');

            generateBtn.text('Generating PDF...');

            $.ajax({
                url: cfb_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfb_generate_pdf',
                    nonce: cfb_ajax.nonce,
                    invoice_id: invoiceId
                },
                success: (response) => {
                    if (response.success) {
                        // Download the PDF
                        window.open(response.data.pdf_url, '_blank');

                        // Show success message
                        this.showSuccessMessage('Invoice generated successfully! The PDF will download automatically.');

                        // Reset invoice form
                        this.resetInvoiceForm();
                    } else {
                        this.showError(response.data || 'Failed to generate PDF');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('PDF generation error:', xhr.responseText);
                    this.showError('Network error occurred while generating PDF');
                },
                complete: () => {
                    generateBtn.prop('disabled', false).text('Generate PDF Invoice');
                }
            });
        }

        resetInvoiceForm() {
            this.wrapper.find('#cfb-want-invoice').prop('checked', false);
            this.wrapper.find('.cfb-invoice-form').hide();
            this.wrapper.find('#cfb-invoice-name, #cfb-invoice-email, #cfb-invoice-phone, #cfb-invoice-address').val('');
        }

        showSuccessMessage(message) {
            // Create or update success message element
            let successElement = this.wrapper.find('.cfb-success-message');
            if (successElement.length === 0) {
                successElement = $('<div class="cfb-success-message" style="display: none;"></div>');
                this.wrapper.find('.cfb-error-message').after(successElement);
            }

            successElement.text(message).show().delay(5000).fadeOut();
        }
    }

    // Initialize calculators when document is ready
    $(document).ready(function() {
        $('.cfb-calculator-wrapper').each(function() {
            new CFBCalculator(this);
        });
    });

    // Make CFBCalculator globally available
    window.cfb_calculator = {
        formatCurrency: function(value) {
            const instance = new CFBCalculator($('.cfb-calculator-wrapper').first());
            return instance.formatCurrency(value);
        }
    };

})(jQuery);
