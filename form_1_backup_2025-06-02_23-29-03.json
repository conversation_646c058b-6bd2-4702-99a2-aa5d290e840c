{"name": "چاپ کاتالوگ", "description": "", "save_submissions": "true", "fields": [{"type": "dropdown", "label": "کاعذ داخلی", "name": "dropdown_9", "required": "false", "width": "half", "position": "left", "pdf_label": "", "options": [{"label": "۱۳۰ گرمی", "value": "130", "pdf_label": ""}, {"label": "۱۵۰ گرمی", "value": "150", "pdf_label": ""}], "conditional_logic": {"enabled": "false"}}, {"type": "dropdown", "label": "کا<PERSON><PERSON> جلد", "name": "dropdown_10", "required": "false", "width": "half", "position": "left", "pdf_label": "", "options": [{"label": "۲۵۰ گرمی", "value": "250", "pdf_label": "۲۵۰ گرمی"}, {"label": "۳۰۰ گرمی", "value": "300", "pdf_label": "۳۰۰ گرمی"}], "conditional_logic": {"enabled": "true", "logic_type": "all"}}, {"type": "slider", "label": "تعداد صفحات", "name": "slider_11", "required": "false", "width": "full", "position": "left", "pdf_label": "", "min": "4", "max": "100", "step": "4", "default_value": "12", "show_value": "true", "conditional_logic": {"enabled": "true", "logic_type": "all"}}, {"type": "dropdown", "label": "پو<PERSON><PERSON> جلد", "name": "dropdown_12", "required": "false", "width": "half", "position": "left", "pdf_label": "", "options": [{"label": "هیچکدام ", "value": "0", "pdf_label": "----"}, {"label": "سلفون براق", "value": "1", "pdf_label": "سلفون براق"}, {"label": "سلفون مات", "value": "2", "pdf_label": "سلفون مات"}], "conditional_logic": {"enabled": "true", "logic_type": "all"}}, {"type": "dropdown", "label": "تیراژ", "name": "dropdown_9", "required": "false", "width": "half", "position": "left", "pdf_label": "", "options": [{"label": "۱۰۰۰ عدد", "value": "1000", "pdf_label": "۱۰۰۰ عدد"}, {"label": "۵۰۰۰ عدد", "value": "5000", "pdf_label": "۵۰۰۰ عدد"}], "conditional_logic": {"enabled": "true", "logic_type": "all"}}, {"type": "dropdown", "label": "پوش<PERSON> جلد سفارشی", "name": "dropdown_14", "required": "false", "width": "full", "position": "left", "pdf_label": "", "options": [{"label": "UV موضعی براق", "value": "1", "pdf_label": ""}, {"label": "UV موضعی مخملی", "value": "2", "pdf_label": ""}], "conditional_logic": {"enabled": "true", "logic_type": "all", "conditions": [{"field": "dropdown_12", "operator": "equals", "value": "2"}]}}, {"type": "total", "label": "Total", "name": "total_15", "required": "false", "width": "full", "position": "left", "pdf_label": "", "display_type": "currency", "formula": "{print} * (1 + 0.2*max(0,ceil(({dropdown_9}+100-5000) / 1000)))", "show_breakdown": "true", "conditional_logic": {"enabled": "true", "logic_type": "all"}}], "settings": {"theme": "default"}}